#!/usr/bin/env python3
"""
Simple test script để kiểm tra ô mã đề
"""

import requests
import json

# URL của API
BASE_URL = "http://localhost:8000"

def test_regular_exam():
    """Test tạo đề thi thông thường với mã đề"""
    
    request_data = {
        "exam_id": "test_exam_001",
        "mon_hoc": "Hoa hoc",
        "lop": 12,
        "ten_truong": "Truong THPT Hong Thinh",
        "ma_de": "0335",  # Thêm mã đề
        "tong_so_cau": 5,
        "cau_hinh_de": [
            {
                "lesson_id": "test1",
                "yeu_cau_can_dat": "Hieu duoc cau tao nguyen tu",
                "muc_do": "Nhan biet",
                "so_cau": 5
            }
        ]
    }
    
    print("=== Testing Regular Exam Generation with Exam Code ===")
    
    try:
        # Gửi request
        response = requests.post(
            f"{BASE_URL}/api/v1/exam/generate-exam-download",
            json=request_data,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("Success!")
            print(f"Content-Type: {response.headers.get('content-type')}")
            print(f"Content-Length: {response.headers.get('content-length')}")
            
            # Lưu file để kiểm tra
            with open("test_exam_with_code.docx", "wb") as f:
                f.write(response.content)
            print("File saved as: test_exam_with_code.docx")
                
        else:
            print("Error!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_regular_exam()
